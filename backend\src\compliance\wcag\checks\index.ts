/**
 * WCAG Checks Index
 * Exports all automated WCAG checks
 */

// Import all check classes
import { ContrastMinimumCheck } from './contrast-minimum';
import { FocusVisibleCheck } from './focus-visible';
import { FocusNotObscuredMinimumCheck } from './focus-not-obscured-minimum';
import { FocusNotObscuredEnhancedCheck } from './focus-not-obscured-enhanced';
import { FocusAppearanceCheck } from './focus-appearance';
import { TargetSizeCheck } from './target-size';
import { NonTextContentCheck } from './non-text-content';
import { InfoRelationshipsCheck } from './info-relationships';
import { KeyboardCheck } from './keyboard';
import { ErrorIdentificationCheck } from './error-identification';
import { NameRoleValueCheck } from './name-role-value';
import { RedundantEntryCheck } from './redundant-entry';
import { ImageAlternatives3Check } from './image-alternatives-3';
import { KeyboardFocus3Check } from './keyboard-focus-3';
import { CaptionsCheck } from './captions';
import { FocusOrderCheck } from './focus-order';
import { DraggingMovementsCheck } from './dragging-movements';
import { ConsistentHelpCheck } from './consistent-help';
import { TextWordingCheck } from './text-wording';
import { MotorCheck } from './motor';
import { PronunciationMeaningCheck } from './pronunciation-meaning';
import { AccessibleAuthenticationCheck } from './accessible-authentication';
import { AccessibleAuthenticationEnhancedCheck } from './accessible-authentication-enhanced';
import { HtmlLangCheck } from './html-lang';
import { LandmarksCheck } from './landmarks';
import { LinkPurposeCheck } from './link-purpose';
import { KeyboardTrapCheck } from './keyboard-trap';
import { BypassBlocksCheck } from './bypass-blocks';
import { PageTitledCheck } from './page-titled';
import { LabelsInstructionsCheck } from './labels-instructions';
import { ErrorSuggestionCheck } from './error-suggestion';
import { ErrorPreventionCheck } from './error-prevention';
import { AudioVideoOnlyCheck } from './audio-video-only';
import { AudioDescriptionCheck } from './audio-description';
import { MultipleWaysCheck } from './multiple-ways';
import { HeadingsLabelsCheck } from './headings-labels';
import { LanguagePartsCheck } from './language-parts';
import { TimingAdjustableCheck } from './timing-adjustable';
import { PauseStopHideCheck } from './pause-stop-hide';

// Re-export all check classes
export { ContrastMinimumCheck } from './contrast-minimum';
export { FocusVisibleCheck } from './focus-visible';
export { FocusNotObscuredMinimumCheck } from './focus-not-obscured-minimum';
export { FocusNotObscuredEnhancedCheck } from './focus-not-obscured-enhanced';
export { FocusAppearanceCheck } from './focus-appearance';
export { TargetSizeCheck } from './target-size';
export { NonTextContentCheck } from './non-text-content';
export { InfoRelationshipsCheck } from './info-relationships';
export { KeyboardCheck } from './keyboard';
export { ErrorIdentificationCheck } from './error-identification';
export { NameRoleValueCheck } from './name-role-value';
export { RedundantEntryCheck } from './redundant-entry';
export { ImageAlternatives3Check } from './image-alternatives-3';
export { KeyboardFocus3Check } from './keyboard-focus-3';
export { CaptionsCheck } from './captions';
export { FocusOrderCheck } from './focus-order';
export { DraggingMovementsCheck } from './dragging-movements';
export { ConsistentHelpCheck } from './consistent-help';
export { TextWordingCheck } from './text-wording';
export { MotorCheck } from './motor';
export { PronunciationMeaningCheck } from './pronunciation-meaning';
export { AccessibleAuthenticationCheck } from './accessible-authentication';
export { AccessibleAuthenticationEnhancedCheck } from './accessible-authentication-enhanced';
export { HtmlLangCheck } from './html-lang';
export { LandmarksCheck } from './landmarks';
export { LinkPurposeCheck } from './link-purpose';
export { KeyboardTrapCheck } from './keyboard-trap';
export { BypassBlocksCheck } from './bypass-blocks';
export { PageTitledCheck } from './page-titled';
export { LabelsInstructionsCheck } from './labels-instructions';
export { ErrorSuggestionCheck } from './error-suggestion';
export { ErrorPreventionCheck } from './error-prevention';
export { AudioVideoOnlyCheck } from './audio-video-only';
export { AudioDescriptionCheck } from './audio-description';
export { MultipleWaysCheck } from './multiple-ways';
export { HeadingsLabelsCheck } from './headings-labels';
export { LanguagePartsCheck } from './language-parts';
export { TimingAdjustableCheck } from './timing-adjustable';
export { PauseStopHideCheck } from './pause-stop-hide';

// Check registry for easy access
export const AUTOMATED_CHECKS = {
  // Part 3: Fully Automated (100%)
  'WCAG-004': 'ContrastMinimumCheck',
  'WCAG-007': 'FocusVisibleCheck',
  'WCAG-010': 'FocusNotObscuredMinimumCheck',
  'WCAG-011': 'FocusNotObscuredEnhancedCheck',
  'WCAG-012': 'FocusAppearanceCheck',
  'WCAG-014': 'TargetSizeCheck',

  // Part 4: Very High Automation (85-95%)
  'WCAG-001': 'NonTextContentCheck',
  'WCAG-003': 'InfoRelationshipsCheck',
  'WCAG-005': 'KeyboardCheck',
  'WCAG-008': 'ErrorIdentificationCheck',
  'WCAG-009': 'NameRoleValueCheck',
  'WCAG-016': 'RedundantEntryCheck',
  'WCAG-017': 'ImageAlternatives3Check',
  'WCAG-019': 'KeyboardFocus3Check',

  // Part 5: High & Medium Automation (60-80%)
  'WCAG-002': 'CaptionsCheck',
  'WCAG-006': 'FocusOrderCheck',
  'WCAG-013': 'DraggingMovementsCheck',
  'WCAG-015': 'ConsistentHelpCheck',
  'WCAG-018': 'TextWordingCheck',
  'WCAG-020': 'MotorCheck',
  'WCAG-021': 'PronunciationMeaningCheck',

  // Enhanced Checks (New Implementation)
  'WCAG-024': 'HtmlLangCheck',
  'WCAG-025': 'LandmarksCheck',
  'WCAG-026': 'LinkPurposeCheck',
  'WCAG-027': 'KeyboardTrapCheck',
  'WCAG-028': 'BypassBlocksCheck',
  'WCAG-029': 'PageTitledCheck',
  'WCAG-030': 'LabelsInstructionsCheck',
  'WCAG-031': 'ErrorSuggestionCheck',
  'WCAG-032': 'ErrorPreventionCheck',
  'WCAG-033': 'AudioVideoOnlyCheck',
  'WCAG-034': 'AudioDescriptionCheck',
  'WCAG-035': 'MultipleWaysCheck',
  'WCAG-036': 'HeadingsLabelsCheck',
  'WCAG-038': 'LanguagePartsCheck',
  'WCAG-044': 'TimingAdjustableCheck',
  'WCAG-045': 'PauseStopHideCheck',
} as const;

/**
 * Get check implementation by rule ID
 */
export function getCheckImplementation(ruleId: string) {
  // Dynamic import approach to avoid circular dependencies
  switch (ruleId) {
    // Part 3: Fully Automated (100%)
    case 'WCAG-004':
      return ContrastMinimumCheck;
    case 'WCAG-007':
      return FocusVisibleCheck;
    case 'WCAG-010':
      return FocusNotObscuredMinimumCheck;
    case 'WCAG-011':
      return FocusNotObscuredEnhancedCheck;
    case 'WCAG-012':
      return FocusAppearanceCheck;
    case 'WCAG-014':
      return TargetSizeCheck;

    // Part 4: Very High Automation (85-95%)
    case 'WCAG-001':
      return NonTextContentCheck;
    case 'WCAG-003':
      return InfoRelationshipsCheck;
    case 'WCAG-005':
      return KeyboardCheck;
    case 'WCAG-008':
      return ErrorIdentificationCheck;
    case 'WCAG-009':
      return NameRoleValueCheck;
    case 'WCAG-016':
      return RedundantEntryCheck;
    case 'WCAG-017':
      return ImageAlternatives3Check;
    case 'WCAG-019':
      return KeyboardFocus3Check;

    // Part 5: High & Medium Automation (60-80%)
    case 'WCAG-002':
      return CaptionsCheck;
    case 'WCAG-006':
      return FocusOrderCheck;
    case 'WCAG-013':
      return DraggingMovementsCheck;
    case 'WCAG-015':
      return ConsistentHelpCheck;
    case 'WCAG-018':
      return TextWordingCheck;
    case 'WCAG-020':
      return MotorCheck;
    case 'WCAG-021':
      return PronunciationMeaningCheck;
    case 'WCAG-022':
      return AccessibleAuthenticationCheck;
    case 'WCAG-023':
      return AccessibleAuthenticationEnhancedCheck;
    case 'WCAG-024':
      return HtmlLangCheck;
    case 'WCAG-025':
      return LandmarksCheck;
    case 'WCAG-026':
      return LinkPurposeCheck;
    case 'WCAG-027':
      return KeyboardTrapCheck;
    case 'WCAG-028':
      return BypassBlocksCheck;
    case 'WCAG-029':
      return PageTitledCheck;
    case 'WCAG-030':
      return LabelsInstructionsCheck;
    case 'WCAG-031':
      return ErrorSuggestionCheck;
    case 'WCAG-032':
      return ErrorPreventionCheck;
    case 'WCAG-033':
      return AudioVideoOnlyCheck;
    case 'WCAG-034':
      return AudioDescriptionCheck;
    case 'WCAG-035':
      return MultipleWaysCheck;
    case 'WCAG-036':
      return HeadingsLabelsCheck;
    case 'WCAG-038':
      return LanguagePartsCheck;
    case 'WCAG-044':
      return TimingAdjustableCheck;
    case 'WCAG-045':
      return PauseStopHideCheck;

    default:
      return undefined;
  }
}

/**
 * Get automation level for a rule
 */
export function getAutomationLevel(ruleId: string): number {
  const automationLevels: Record<string, number> = {
    'WCAG-001': 0.95,
    'WCAG-002': 0.8,
    'WCAG-003': 0.9,
    'WCAG-004': 1.0,
    'WCAG-005': 0.85,
    'WCAG-006': 0.75,
    'WCAG-007': 1.0,
    'WCAG-008': 0.9,
    'WCAG-009': 0.9,
    'WCAG-010': 1.0,
    'WCAG-011': 1.0,
    'WCAG-012': 1.0,
    'WCAG-013': 0.7,
    'WCAG-014': 1.0,
    'WCAG-015': 0.8,
    'WCAG-016': 0.85,
    'WCAG-017': 0.95,
    'WCAG-018': 0.75,
    'WCAG-019': 0.9,
    'WCAG-020': 0.8,
    'WCAG-021': 0.6,
    'WCAG-022': 0.5,
    'WCAG-023': 0.4,
    'WCAG-024': 1.0,  // HTML Language Check - fully automated
    'WCAG-025': 0.95, // Landmarks Check - very high automation
    'WCAG-026': 0.90, // Link Purpose Check - high automation
    'WCAG-027': 0.85, // Keyboard Trap Check - high automation
    'WCAG-028': 0.9, // Bypass Blocks Check - very high automation
    'WCAG-029': 1.0, // Page Titled Check - fully automated
    'WCAG-030': 0.8, // Labels or Instructions Check - high automation
    'WCAG-031': 0.75, // Error Suggestion Check - high automation
    'WCAG-032': 0.7, // Error Prevention Check - medium-high automation
    'WCAG-033': 0.65, // Audio-only and Video-only Check - medium automation
    'WCAG-034': 0.6, // Audio Description Check - medium automation
    'WCAG-035': 0.7, // Multiple Ways Check - medium-high automation
    'WCAG-036': 0.8, // Headings and Labels Check - high automation
    'WCAG-038': 0.65, // Language of Parts Check - medium automation
    'WCAG-044': 0.85, // Timing Adjustable Check - high automation
    'WCAG-045': 0.8, // Pause, Stop, Hide Check - high automation
  };
  return automationLevels[ruleId] || 0;
}

export type AutomatedCheckType = keyof typeof AUTOMATED_CHECKS;
